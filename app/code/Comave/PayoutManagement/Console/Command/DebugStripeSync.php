<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Comave\PayoutManagement\Service\StripePayoutService;
use Comave\PayoutManagement\Service\PayoutSyncService;
use Comave\PayoutManagement\Model\ConfigProvider;
use Magento\Framework\App\State;

/**
 * Debug Stripe Sync CLI Command
 *
 * Helps debug issues with Stripe payout synchronization
 */
class DebugStripeSync extends Command
{
    /**
     * Constructor
     *
     * @param StripePayoutService $stripePayoutService
     * @param PayoutSyncService $payoutSyncService
     * @param ConfigProvider $configProvider
     * @param State $appState
     */
    public function __construct(
        private readonly StripePayoutService $stripePayoutService,
        private readonly PayoutSyncService $payoutSyncService,
        private readonly ConfigProvider $configProvider,
        private readonly State $appState
    ) {
        parent::__construct();
    }

    /**
     * Configure command
     *
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('comave:payouts:debug')
            ->setDescription('Debug Stripe payout synchronization issues');
    }

    /**
     * Execute command
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->appState->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);
        } catch (\Exception $e) {
            // Area already set
        }

        $output->writeln('<info>🔍 Debugging Stripe Payout Sync...</info>');
        $output->writeln('');

        // Check if module is enabled
        $output->writeln('<comment>1. Checking module configuration...</comment>');
        $isEnabled = $this->configProvider->isEnabled();
        $output->writeln("   Module enabled: " . ($isEnabled ? '✅ Yes' : '❌ No'));
        
        if (!$isEnabled) {
            $output->writeln('<error>❌ Payout Management module is disabled!</error>');
            return Command::FAILURE;
        }

        // Test Stripe connection and get connected accounts
        $output->writeln('<comment>2. Testing Stripe connection...</comment>');
        try {
            $allPayouts = $this->stripePayoutService->getAllPayouts();
            $output->writeln("   Stripe connection: ✅ Success");
            $output->writeln("   Total payouts found: " . count($allPayouts));
            
            if (empty($allPayouts)) {
                $output->writeln('<warning>⚠️  No payouts found from Stripe API</warning>');
                
                // Let's debug further
                $this->debugConnectedAccounts($output);
                $this->debugStripeApiDirectly($output);
            } else {
                $output->writeln('<info>✅ Found ' . count($allPayouts) . ' payouts from Stripe</info>');
                
                // Show sample payout data
                $output->writeln('<comment>3. Sample payout data:</comment>');
                $samplePayout = array_slice($allPayouts, 0, 1)[0] ?? null;
                if ($samplePayout) {
                    foreach ($samplePayout as $key => $value) {
                        $output->writeln("   {$key}: {$value}");
                    }
                }
            }
            
        } catch (\Exception $e) {
            $output->writeln("   Stripe connection: ❌ Failed");
            $output->writeln("   Error: " . $e->getMessage());
            return Command::FAILURE;
        }

        // Test sync process
        $output->writeln('<comment>4. Testing sync process...</comment>');
        try {
            $syncResult = $this->payoutSyncService->syncPayouts();
            
            if ($syncResult['success']) {
                $output->writeln("   Sync result: ✅ Success");
                $output->writeln("   Added: " . $syncResult['added']);
                $output->writeln("   Updated: " . $syncResult['updated']);
                
                if (!empty($syncResult['errors'])) {
                    $output->writeln("   Errors: " . count($syncResult['errors']));
                    foreach ($syncResult['errors'] as $error) {
                        $output->writeln("     - {$error}");
                    }
                }
            } else {
                $output->writeln("   Sync result: ❌ Failed");
                $output->writeln("   Error: " . $syncResult['error']);
            }
            
        } catch (\Exception $e) {
            $output->writeln("   Sync process: ❌ Failed");
            $output->writeln("   Error: " . $e->getMessage());
        }

        return Command::SUCCESS;
    }

    /**
     * Debug connected accounts
     *
     * @param OutputInterface $output
     * @return void
     */
    private function debugConnectedAccounts(OutputInterface $output): void
    {
        $output->writeln('<comment>   Debugging connected accounts...</comment>');
        
        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->stripePayoutService);
        $method = $reflection->getMethod('getConnectedAccounts');
        $method->setAccessible(true);
        
        try {
            $accounts = $method->invoke($this->stripePayoutService);
            $output->writeln("   Connected accounts found: " . count($accounts));
            
            if (empty($accounts)) {
                $output->writeln('<warning>   ⚠️  No connected Stripe accounts found!</warning>');
                $output->writeln('   This could mean:');
                $output->writeln('   - No sellers have connected their Stripe accounts');
                $output->writeln('   - The seller collection query is not finding data');
                $output->writeln('   - Database table structure issues');
            } else {
                foreach ($accounts as $i => $account) {
                    $output->writeln("   Account {$i}: Seller ID {$account['seller_id']}, Stripe Account: {$account['stripe_account_id']}");
                }
            }
        } catch (\Exception $e) {
            $output->writeln("   Error getting connected accounts: " . $e->getMessage());
        }
    }

    /**
     * Debug Stripe API directly
     *
     * @param OutputInterface $output
     * @return void
     */
    private function debugStripeApiDirectly(OutputInterface $output): void
    {
        $output->writeln('<comment>   Testing direct Stripe API call...</comment>');
        
        try {
            // Use reflection to access private method
            $reflection = new \ReflectionClass($this->stripePayoutService);
            $clientMethod = $reflection->getMethod('getStripeClient');
            $clientMethod->setAccessible(true);
            
            $stripeClient = $clientMethod->invoke($this->stripePayoutService);
            
            // Test direct API call to main account
            $payouts = $stripeClient->payouts->all(['limit' => 5]);
            $output->writeln("   Direct API call result: " . count($payouts->data) . " payouts found in main account");
            
        } catch (\Exception $e) {
            $output->writeln("   Direct API call failed: " . $e->getMessage());
        }
    }
}

<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Comave\PayoutManagement\Model\ResourceModel\Payout as PayoutResource;
use Magento\Framework\App\State;

/**
 * Clear Payouts CLI Command
 *
 * Provides CLI command to clear payout data from database
 */
class ClearPayouts extends Command
{
    private const OPTION_FORCE = 'force';
    private const OPTION_STATUS = 'status';

    /**
     * Constructor
     *
     * @param PayoutResource $payoutResource
     * @param State $appState
     */
    public function __construct(
        private readonly PayoutResource $payoutResource,
        private readonly State $appState
    ) {
        parent::__construct();
    }

    /**
     * Configure command
     *
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('comave:payouts:clear')
            ->setDescription('Clear payout data from database')
            ->addOption(
                self::OPTION_FORCE,
                'f',
                InputOption::VALUE_NONE,
                'Force deletion without confirmation'
            )
            ->addOption(
                self::OPTION_STATUS,
                's',
                InputOption::VALUE_OPTIONAL,
                'Clear only specific status (paid, failed, pending, in_transit)'
            );
    }

    /**
     * Execute command
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->appState->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);
        } catch (\Exception $e) {
            // Area already set
        }

        $force = $input->getOption(self::OPTION_FORCE);
        $status = $input->getOption(self::OPTION_STATUS);

        // Confirmation
        if (!$force) {
            $helper = $this->getHelper('question');
            $message = $status 
                ? "Are you sure you want to clear all payouts with status '{$status}'? [y/N] "
                : 'Are you sure you want to clear ALL payout data? [y/N] ';
            
            $question = new ConfirmationQuestion($message, false);
            
            if (!$helper->ask($input, $output, $question)) {
                $output->writeln('<info>Operation cancelled.</info>');
                return Command::SUCCESS;
            }
        }

        // Clear payouts
        $deletedCount = $this->clearPayouts($status);

        if ($status) {
            $output->writeln("<info>Cleared {$deletedCount} payouts with status '{$status}'.</info>");
        } else {
            $output->writeln("<info>Cleared {$deletedCount} payout records.</info>");
        }

        return Command::SUCCESS;
    }

    /**
     * Clear payouts from database
     *
     * @param string|null $status
     * @return int
     */
    private function clearPayouts(?string $status): int
    {
        $connection = $this->payoutResource->getConnection();
        $tableName = $this->payoutResource->getMainTable();

        if ($status) {
            return $connection->delete($tableName, ['status = ?' => $status]);
        } else {
            // Clear all records
            $connection->query("TRUNCATE TABLE {$tableName}");
            return $connection->query("SELECT ROW_COUNT()")->fetchColumn();
        }
    }
}

<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Controller\Adminhtml\Export;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Response\Http\FileFactory;
use Comave\PayoutManagement\Model\ResourceModel\Payout\CollectionFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\File\Csv;
use Comave\PayoutManagement\Model\ResourceModel\Payout\Collection;

/**
 * Upcoming Payouts CSV Export Controller
 *
 * Exports upcoming payouts data to CSV format
 */
class UpcomingCsv extends Action implements HttpGetActionInterface
{
    /**
     * Admin resource for ACL
     */
    public const ADMIN_RESOURCE = 'Comave_PayoutManagement::export_payouts';

    /**
     * Constructor
     *
     * @param Context $context
     * @param FileFactory $fileFactory
     * @param CollectionFactory $collectionFactory
     * @param Csv $csvProcessor
     */
    public function __construct(
        Context $context,
        private readonly FileFactory $fileFactory,
        private readonly CollectionFactory $collectionFactory,
        private readonly Csv $csvProcessor
    ) {
        parent::__construct($context);
    }

    /**
     * Execute CSV export for upcoming payouts
     *
     * @return ResponseInterface
     */
    public function execute(): ResponseInterface
    {
        $fileName = 'upcoming_payouts_' . date('Y-m-d_H-i-s') . '.csv';

        $collection = $this->collectionFactory->create();
        $collection->addFieldToFilter('status', ['in' => ['pending', 'in_transit']]);

        $csvContent = $this->generateCsvContent($collection);

        return $this->fileFactory->create(
            $fileName,
            $csvContent,
            DirectoryList::VAR_DIR,
            'text/csv'
        );
    }

    /**
     * Generate CSV content using proper CSV processor
     *
     * @param Collection $collection
     * @return string
     */
    private function generateCsvContent(Collection $collection): string
    {
        $csvData = [];

        // Add header row
        $csvData[] = [
            'Seller ID',
            'Seller Name',
            'Payout Amount (EUR)',
            'Scheduled Date',
            'Payment Method',
            'Status',
            'Last Sync'
        ];

        // Add data rows
        foreach ($collection as $payout) {
            $csvData[] = [
                $payout->getSellerId(),
                $payout->getSellerName() ?? '',
                number_format((float)$payout->getAmount(), 2),
                $payout->getScheduledDate() ?? '',
                $payout->getPaymentMethod(),
                $payout->getStatus(),
                $payout->getLastSyncAt() ?? ''
            ];
        }

        // Use temporary file to generate CSV content
        $tmpFile = tmpfile();
        $tmpPath = stream_get_meta_data($tmpFile)['uri'];

        $this->csvProcessor->saveData($tmpPath, $csvData);
        $csvContent = file_get_contents($tmpPath);

        fclose($tmpFile);

        return $csvContent;
    }
}

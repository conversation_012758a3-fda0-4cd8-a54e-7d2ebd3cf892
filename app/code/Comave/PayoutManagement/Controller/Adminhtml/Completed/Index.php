<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Controller\Adminhtml\Completed;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\View\Result\Page;

/**
 * Completed Payouts Index Controller
 *
 * Displays the completed payouts listing page in admin
 */
class Index extends Action implements HttpGetActionInterface
{
    /**
     * Admin resource for ACL
     */
    public const ADMIN_RESOURCE = 'Comave_PayoutManagement::completed_payouts';

    /**
     * Constructor
     *
     * @param Context $context
     * @param PageFactory $resultPageFactory
     */
    public function __construct(
        Context $context,
        private readonly PageFactory $resultPageFactory
    ) {
        parent::__construct($context);
    }

    /**
     * Execute action to display completed payouts page
     *
     * @return Page
     */
    public function execute(): Page
    {
        $resultPage = $this->resultPageFactory->create();
        $resultPage->setActiveMenu('Comave_PayoutManagement::completed_payouts');
        $resultPage->getConfig()->getTitle()->prepend(__('Completed Payouts'));

        return $resultPage;
    }
}

<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Controller\Adminhtml\Upcoming;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\View\Result\Page;

/**
 * Upcoming Payouts Index Controller
 *
 * Displays the upcoming payouts listing page in admin
 */
class Index extends Action implements HttpGetActionInterface
{
    /**
     * Admin resource for ACL
     */
    public const ADMIN_RESOURCE = 'Comave_PayoutManagement::upcoming_payouts';

    /**
     * Constructor
     *
     * @param Context $context
     * @param PageFactory $resultPageFactory
     */
    public function __construct(
        Context $context,
        private readonly PageFactory $resultPageFactory
    ) {
        parent::__construct($context);
    }

    /**
     * Execute action to display upcoming payouts page
     *
     * @return Page
     */
    public function execute(): Page
    {
        $resultPage = $this->resultPageFactory->create();
        $resultPage->setActiveMenu('Comave_PayoutManagement::upcoming_payouts');
        $resultPage->getConfig()->getTitle()->prepend(__('Upcoming Payouts'));

        return $resultPage;
    }
}

<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Controller\Adminhtml\Refresh;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\Result\Json;
use Comave\PayoutManagement\Service\PayoutSyncService;
use Psr\Log\LoggerInterface;

/**
 * Refresh Payouts Controller
 *
 * Handles AJAX requests to refresh payout data from Stripe
 */
class Index extends Action implements HttpPostActionInterface
{
    /**
     * Admin resource for ACL
     */
    public const ADMIN_RESOURCE = 'Comave_PayoutManagement::refresh_payouts';

    /**
     * Constructor
     *
     * @param Context $context
     * @param PayoutSyncService $payoutSyncService
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        private readonly PayoutSyncService $payoutSyncService,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct($context);
    }

    /**
     * Execute AJAX refresh action
     *
     * @return Json
     */
    public function execute(): Json
    {
        /** @var Json $result */
        $result = $this->resultFactory->create(ResultFactory::TYPE_JSON);

        try {
            $syncResult = $this->payoutSyncService->syncPayouts();

            if ($syncResult['success']) {
                $message = __('Payouts synchronized successfully. Updated: %1, Added: %2',
                             $syncResult['updated'],
                             $syncResult['added']);

                return $result->setData([
                    'success' => true,
                    'message' => $message->render(),
                    'updated' => $syncResult['updated'],
                    'added' => $syncResult['added'],
                    'reload' => true
                ]);
            } else {
                $errorMessage = __('Failed to synchronize payouts: %1', $syncResult['error'] ?? 'Unknown error');

                return $result->setData([
                    'success' => false,
                    'message' => $errorMessage->render()
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error('Error refreshing payouts: ' . $e->getMessage());

            return $result->setData([
                'success' => false,
                'message' => __('An error occurred while refreshing payouts.')->render()
            ]);
        }
    }
}

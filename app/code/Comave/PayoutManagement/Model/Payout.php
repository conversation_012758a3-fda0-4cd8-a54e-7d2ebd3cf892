<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Model;

use Magento\Framework\Model\AbstractModel;
use Comave\PayoutManagement\Api\Data\PayoutInterface;

/**
 * Payout Model
 *
 * Represents a payout entity with data from Stripe
 */
class Payout extends AbstractModel implements PayoutInterface
{
    /**
     * Cache tag for payout entities
     */
    public const CACHE_TAG = 'comave_payout_management';

    /**
     * Cache tag
     */
    protected $_cacheTag = self::CACHE_TAG;

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct(): void
    {
        $this->_init(\Comave\PayoutManagement\Model\ResourceModel\Payout::class);
    }

    /**
     * Get Stripe payout ID
     *
     * @return string|null
     */
    public function getStripePayoutId(): ?string
    {
        return $this->getData(self::STRIPE_PAYOUT_ID);
    }

    /**
     * Set Stripe payout ID
     *
     * @param string $stripePayoutId
     * @return PayoutInterface
     */
    public function setStripePayoutId(string $stripePayoutId): PayoutInterface
    {
        return $this->setData(self::STRIPE_PAYOUT_ID, $stripePayoutId);
    }

    /**
     * Get seller ID
     *
     * @return int|null
     */
    public function getSellerId(): ?int
    {
        $value = $this->getData(self::SELLER_ID);
        return $value ? (int)$value : null;
    }

    /**
     * Set seller ID
     *
     * @param int $sellerId
     * @return PayoutInterface
     */
    public function setSellerId(int $sellerId): PayoutInterface
    {
        return $this->setData(self::SELLER_ID, $sellerId);
    }

    /**
     * Get seller name
     *
     * @return string|null
     */
    public function getSellerName(): ?string
    {
        return $this->getData(self::SELLER_NAME);
    }

    /**
     * Set seller name
     *
     * @param string|null $sellerName
     * @return PayoutInterface
     */
    public function setSellerName(?string $sellerName): PayoutInterface
    {
        return $this->setData(self::SELLER_NAME, $sellerName);
    }

    /**
     * Get Stripe account ID
     *
     * @return string|null
     */
    public function getStripeAccountId(): ?string
    {
        return $this->getData(self::STRIPE_ACCOUNT_ID);
    }

    /**
     * Set Stripe account ID
     *
     * @param string $stripeAccountId
     * @return PayoutInterface
     */
    public function setStripeAccountId(string $stripeAccountId): PayoutInterface
    {
        return $this->setData(self::STRIPE_ACCOUNT_ID, $stripeAccountId);
    }

    /**
     * Get payout amount
     *
     * @return float|null
     */
    public function getAmount(): ?float
    {
        $value = $this->getData(self::AMOUNT);
        return $value ? (float)$value : null;
    }

    /**
     * Set payout amount
     *
     * @param float $amount
     * @return PayoutInterface
     */
    public function setAmount(float $amount): PayoutInterface
    {
        return $this->setData(self::AMOUNT, $amount);
    }

    /**
     * Get currency code
     *
     * @return string|null
     */
    public function getCurrency(): ?string
    {
        return $this->getData(self::CURRENCY);
    }

    /**
     * Set currency code
     *
     * @param string $currency
     * @return PayoutInterface
     */
    public function setCurrency(string $currency): PayoutInterface
    {
        return $this->setData(self::CURRENCY, $currency);
    }

    /**
     * Get payout status
     *
     * @return string|null
     */
    public function getStatus(): ?string
    {
        return $this->getData(self::STATUS);
    }

    /**
     * Set payout status
     *
     * @param string $status
     * @return PayoutInterface
     */
    public function setStatus(string $status): PayoutInterface
    {
        return $this->setData(self::STATUS, $status);
    }

    /**
     * Get payment method
     *
     * @return string|null
     */
    public function getPaymentMethod(): ?string
    {
        return $this->getData(self::PAYMENT_METHOD);
    }

    /**
     * Set payment method
     *
     * @param string $paymentMethod
     * @return PayoutInterface
     */
    public function setPaymentMethod(string $paymentMethod): PayoutInterface
    {
        return $this->setData(self::PAYMENT_METHOD, $paymentMethod);
    }

    /**
     * Get scheduled date
     *
     * @return string|null
     */
    public function getScheduledDate(): ?string
    {
        return $this->getData(self::SCHEDULED_DATE);
    }

    /**
     * Set scheduled date
     *
     * @param string|null $scheduledDate
     * @return PayoutInterface
     */
    public function setScheduledDate(?string $scheduledDate): PayoutInterface
    {
        return $this->setData(self::SCHEDULED_DATE, $scheduledDate);
    }

    /**
     * Get completion date
     *
     * @return string|null
     */
    public function getCompletionDate(): ?string
    {
        return $this->getData(self::COMPLETION_DATE);
    }

    /**
     * Set completion date
     *
     * @param string|null $completionDate
     * @return PayoutInterface
     */
    public function setCompletionDate(?string $completionDate): PayoutInterface
    {
        return $this->setData(self::COMPLETION_DATE, $completionDate);
    }

    /**
     * Get last sync timestamp
     *
     * @return string|null
     */
    public function getLastSyncAt(): ?string
    {
        return $this->getData(self::LAST_SYNC_AT);
    }

    /**
     * Set last sync timestamp
     *
     * @param string|null $lastSyncAt
     * @return PayoutInterface
     */
    public function setLastSyncAt(?string $lastSyncAt): PayoutInterface
    {
        return $this->setData(self::LAST_SYNC_AT, $lastSyncAt);
    }
}

<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Model\ResourceModel;

use Magento\Framework\Model\ResourceModel\Db\AbstractDb;

/**
 * Payout Resource Model
 *
 * Handles database operations for payout entities
 */
class Payout extends AbstractDb
{
    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct(): void
    {
        $this->_init('comave_payout_management', 'entity_id');
    }
}

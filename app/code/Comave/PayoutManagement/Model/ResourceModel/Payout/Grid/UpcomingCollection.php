<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Model\ResourceModel\Payout\Grid;

use Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult;

/**
 * Upcoming Payouts Grid Collection
 *
 * Collection for upcoming payouts grid with automatic status filtering
 */
class UpcomingCollection extends SearchResult
{
    /**
     * Initialize collection
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(
            \Comave\PayoutManagement\Model\Payout::class,
            \Comave\PayoutManagement\Model\ResourceModel\Payout::class
        );
        $this->_map['fields']['entity_id'] = 'main_table.entity_id';
    }

    /**
     * Initialize select
     *
     * @return $this
     */
    protected function _initSelect()
    {
        parent::_initSelect();

        // Automatically filter for upcoming payouts
        $this->addFieldToFilter('status', ['in' => ['pending', 'in_transit']]);

        return $this;
    }
}

<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Model\ResourceModel\Payout;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Comave\PayoutManagement\Model\Payout;
use Comave\PayoutManagement\Model\ResourceModel\Payout as PayoutResource;

/**
 * Payout Collection
 *
 * Collection class for payout entities
 */
class Collection extends AbstractCollection
{
    /**
     * ID field name
     */
    protected $_idFieldName = 'entity_id';

    /**
     * Initialize collection
     *
     * @return void
     */
    protected function _construct(): void
    {
        $this->_init(Payout::class, PayoutResource::class);
    }
}

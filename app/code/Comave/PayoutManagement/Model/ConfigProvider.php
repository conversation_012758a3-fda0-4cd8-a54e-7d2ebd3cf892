<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * Payout Management Configuration Provider
 *
 * Provides configuration values for the payout management module
 */
class ConfigProvider
{
    /**
     * Configuration paths
     */
    private const string XML_PATH_ENABLED = 'payout_management/general/enabled';

    /**
     * Constructor
     *
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig
    ) {
    }

    /**
     * Check if payout management is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }


}

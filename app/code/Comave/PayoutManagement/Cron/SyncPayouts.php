<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Cron;

use Comave\PayoutManagement\Service\PayoutSyncService;
use Comave\PayoutManagement\Model\ConfigProvider;
use Psr\Log\LoggerInterface;

/**
 * Sync Payouts Cron Job
 *
 * Scheduled task to synchronize payout data from Stripe
 */
class SyncPayouts
{
    /**
     * Constructor
     *
     * @param PayoutSyncService $payoutSyncService
     * @param ConfigProvider $configProvider
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly PayoutSyncService $payoutSyncService,
        private readonly ConfigProvider $configProvider,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Execute cron job to sync payouts
     *
     * @return void
     */
    public function execute(): void
    {
        try {
            if (!$this->configProvider->isEnabled()) {
                $this->logger->info('Payout management is disabled, skipping sync');
                return;
            }

            $this->logger->info('Starting scheduled payout synchronization');
            $result = $this->payoutSyncService->syncPayouts();
            if ($result['success']) {
                $this->logger->info(
                    'Payout synchronization completed successfully',
                    ['updated' => $result['updated'], 'added' => $result['added']]
                );
            } else {
                $this->logger->error('Payout synchronization failed: ' . $result['error']);
            }
        } catch (\Exception $e) {
            $this->logger->error('Error in scheduled payout sync: ' . $e->getMessage());
        }
    }
}

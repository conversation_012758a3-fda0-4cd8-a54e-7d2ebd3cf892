<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="upcoming_payouts_listing_data_source" xsi:type="string">ComaveUpcomingPayoutsGridCollection</item>
                <item name="completed_payouts_listing_data_source" xsi:type="string">ComaveCompletedPayoutsGridCollection</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="ComaveUpcomingPayoutsGridCollection" type="Comave\PayoutManagement\Model\ResourceModel\Payout\Grid\UpcomingCollection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_payout_management</argument>
            <argument name="resourceModel" xsi:type="string">Comave\PayoutManagement\Model\ResourceModel\Payout</argument>
        </arguments>
    </virtualType>

    <virtualType name="ComaveCompletedPayoutsGridCollection" type="Comave\PayoutManagement\Model\ResourceModel\Payout\Grid\CompletedCollection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_payout_management</argument>
            <argument name="resourceModel" xsi:type="string">Comave\PayoutManagement\Model\ResourceModel\Payout</argument>
        </arguments>
    </virtualType>

    
</config>

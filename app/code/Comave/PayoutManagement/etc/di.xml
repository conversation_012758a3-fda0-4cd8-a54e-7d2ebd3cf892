<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="upcoming_payouts_listing_data_source" xsi:type="string">ComavePayoutManagementSearchResult</item>
                <item name="completed_payouts_listing_data_source" xsi:type="string">ComavePayoutManagementSearchResult</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="ComavePayoutManagementSearchResult" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_payout_management</argument>
            <argument name="resourceModel" xsi:type="string">Comave\PayoutManagement\Model\ResourceModel\Payout</argument>
        </arguments>
    </virtualType>

    <type name="Comave\PayoutManagement\Ui\DataProvider\UpcomingPayoutsDataProvider">
        <arguments>
            <argument name="name" xsi:type="string">upcoming_payouts_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">entity_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
        </arguments>
    </type>

    <type name="Comave\PayoutManagement\Ui\DataProvider\CompletedPayoutsDataProvider">
        <arguments>
            <argument name="name" xsi:type="string">completed_payouts_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">entity_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
        </arguments>
    </type>

    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="comave_debug_stripe_sync" xsi:type="object">Comave\PayoutManagement\Console\Command\DebugStripeSync</item>
            </argument>
        </arguments>
    </type>


    
</config>

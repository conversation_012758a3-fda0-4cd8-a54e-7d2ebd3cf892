<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
    <group id="comave_group">
        <job name="comave_payout_management_sync" instance="Comave\PayoutManagement\Cron\SyncPayouts" method="execute">
            <schedule>0 */6 * * *</schedule>
        </job>
    </group>
</config>

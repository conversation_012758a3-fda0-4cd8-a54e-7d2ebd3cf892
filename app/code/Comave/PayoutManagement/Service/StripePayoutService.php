<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Service;

use Comave\SellerPayouts\Helper\Data as SellerPayoutsHelper;
use Comave\SellerPayouts\Helper\Payout\Collections as PayoutCollections;
use Magento\Customer\Model\CustomerFactory;
use Psr\Log\LoggerInterface;
use Stripe\StripeClient;
use Stripe\Payout;
use Stripe\Exception\ApiErrorException;

class StripePayoutService
{
    private const PAYOUT_STATUS_MAPPING = [
        'pending' => 'pending',
        'in_transit' => 'in_transit',
        'paid' => 'paid',
        'failed' => 'failed'
    ];

    /**
     * Cached Stripe client instance
     */
    private ?StripeClient $stripeClient = null;

    /**
     * Cached connected accounts
     */
    private ?array $connectedAccounts = null;

    public function __construct(
        private readonly SellerPayoutsHelper $sellerPayoutsHelper,
        private readonly PayoutCollections $payoutCollections,
        private readonly CustomerFactory $customerFactory,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Get all payouts from Stripe for connected accounts
     */
    public function getAllPayouts(): array
    {
        try {
            $this->logger->info('Starting payout fetch from Stripe');

            $stripeClient = $this->getStripeClient();
            $this->logger->info('Stripe client initialized successfully');

            $connectedAccounts = $this->getConnectedAccounts();
            $this->logger->info('Connected accounts retrieved', ['count' => count($connectedAccounts)]);

            if (empty($connectedAccounts)) {
                $this->logger->warning('No connected accounts found - no payouts will be fetched');
                return [];
            }

            $allPayouts = [];

            foreach ($connectedAccounts as $account) {
                try {
                    $this->logger->info('Fetching payouts for account', [
                        'seller_id' => $account['seller_id'],
                        'stripe_account_id' => substr($account['stripe_account_id'], 0, 10) . '...'
                    ]);

                    $payouts = $this->getPayoutsForAccount($stripeClient, $account);
                    $this->logger->info('Payouts fetched for account', [
                        'seller_id' => $account['seller_id'],
                        'payout_count' => count($payouts)
                    ]);

                    $allPayouts = array_merge($allPayouts, $payouts);
                } catch (\Exception $e) {
                    $this->logger->error('Error fetching payouts for specific account', [
                        'seller_id' => $account['seller_id'],
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    // Continue with other accounts even if one fails
                }
            }

            $this->logger->info('Total payouts fetched from Stripe', ['total_count' => count($allPayouts)]);
            return $allPayouts;
        } catch (\Exception $e) {
            $this->logger->error('Critical error in getAllPayouts', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e; // Re-throw to let the calling code handle it
        }
    }

    /**
     * Get cached Stripe client instance
     *
     * @return StripeClient
     * @throws \Exception
     */
    private function getStripeClient(): StripeClient
    {
        if ($this->stripeClient === null) {
            $secretKey = $this->sellerPayoutsHelper->getDecryptedKey();
            if (empty($secretKey)) {
                throw new \Exception('Stripe secret key not configured');
            }

            $this->stripeClient = new StripeClient($secretKey);
        }

        return $this->stripeClient;
    }

    /**
     * Get cached connected Stripe accounts with optimized query
     *
     * @return array
     */
    private function getConnectedAccounts(): array
    {
        if ($this->connectedAccounts === null) {
            $this->connectedAccounts = [];

            try {
                $this->logger->info('Starting to get connected accounts');

                // Get seller collection - this already has stripe_client_id joined as a field
                $sellerCollection = $this->payoutCollections->getSellerCollection();
                $this->logger->info('Seller collection created');

                // Filter for sellers with stripe_client_id (it's already joined as a field, not an EAV attribute)
                $sellerCollection->addFieldToFilter('stripe_client_id', ['notnull' => true]);
                $sellerCollection->addFieldToFilter('stripe_client_id', ['neq' => '']);
                $this->logger->info('Filters applied to seller collection');

                $this->logger->info('About to iterate through seller collection', ['collection_size' => $sellerCollection->getSize()]);

                foreach ($sellerCollection as $seller) {
                    $this->logger->info('Processing seller', ['seller_id' => $seller->getSellerId()]);

                    $stripeClientId = $seller->getData('stripe_client_id');
                    $this->logger->info('Seller stripe_client_id', [
                        'seller_id' => $seller->getSellerId(),
                        'has_stripe_id' => !empty($stripeClientId),
                        'stripe_id_length' => $stripeClientId ? strlen($stripeClientId) : 0
                    ]);

                    if (!empty($stripeClientId)) {
                        // Get customer data for seller name
                        $customer = $this->customerFactory->create()->load($seller->getSellerId());
                        $sellerName = trim($customer->getFirstname() . ' ' . $customer->getLastname()) ?: 'Unknown Seller';

                        $this->connectedAccounts[] = [
                            'seller_id' => $seller->getSellerId(),
                            'seller_name' => $sellerName,
                            'stripe_account_id' => $stripeClientId
                        ];

                        $this->logger->info('Added connected account', [
                            'seller_id' => $seller->getSellerId(),
                            'seller_name' => $sellerName
                        ]);
                    }
                }

                $this->logger->info('Connected accounts found', [
                    'count' => count($this->connectedAccounts),
                    'accounts' => array_map(function($account) {
                        return [
                            'seller_id' => $account['seller_id'],
                            'seller_name' => $account['seller_name'],
                            'stripe_account_id' => substr($account['stripe_account_id'], 0, 10) . '...' // Partial for security
                        ];
                    }, $this->connectedAccounts)
                ]);

            } catch (\Exception $e) {
                $this->logger->error('Error getting connected accounts', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                $this->connectedAccounts = [];
            }
        }

        return $this->connectedAccounts;
    }

    /**
     * Get payouts for specific Stripe account
     *
     * @param StripeClient $stripeClient
     * @param array $account
     * @return array
     */
    private function getPayoutsForAccount(StripeClient $stripeClient, array $account): array
    {
        try {
            $this->logger->info('Making Stripe API call for payouts', [
                'seller_id' => $account['seller_id'],
                'stripe_account_id' => substr($account['stripe_account_id'], 0, 10) . '...'
            ]);

            $payouts = $stripeClient->payouts->all([
                'limit' => 100,
                'expand' => ['data.destination']
            ], [
                'stripe_account' => $account['stripe_account_id']
            ]);

            $this->logger->info('Stripe API response received', [
                'seller_id' => $account['seller_id'],
                'raw_payout_count' => count($payouts->data)
            ]);

            $formattedPayouts = [];
            foreach ($payouts->data as $payout) {
                $formattedPayouts[] = $this->formatPayoutData($payout, $account);
            }

            return $formattedPayouts;
        } catch (ApiErrorException $e) {
            $this->logger->error('Stripe API error fetching payouts', [
                'error_message' => $e->getMessage(),
                'error_code' => $e->getStripeCode(),
                'seller_id' => $account['seller_id'],
                'stripe_account_id' => substr($account['stripe_account_id'], 0, 10) . '...'
            ]);
            return [];
        } catch (\Exception $e) {
            $this->logger->error('General error fetching payouts', [
                'error_message' => $e->getMessage(),
                'seller_id' => $account['seller_id']
            ]);
            return [];
        }
    }

    /**
     * Format payout data for consistent structure
     *
     * @param Payout $payout
     * @param array $account
     * @return array
     */
    private function formatPayoutData(Payout $payout, array $account): array
    {
        return [
            'stripe_payout_id' => $payout->id,
            'seller_id' => $account['seller_id'],
            'seller_name' => $account['seller_name'],
            'stripe_account_id' => $account['stripe_account_id'],
            'amount' => $payout->amount / 100, // Convert from cents
            'currency' => strtoupper($payout->currency),
            'status' => $this->mapStripeStatus($payout->status),
            'payment_method' => 'stripe',
            'scheduled_date' => $payout->arrival_date ? date('Y-m-d H:i:s', $payout->arrival_date) : null,
            'completion_date' => $payout->status === 'paid' ? date('Y-m-d H:i:s', $payout->created) : null,
            'created_at' => date('Y-m-d H:i:s', $payout->created),
            'last_sync_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Map Stripe payout status to our internal status
     *
     * @param string $stripeStatus
     * @return string
     */
    private function mapStripeStatus(string $stripeStatus): string
    {
        return self::PAYOUT_STATUS_MAPPING[$stripeStatus] ?? 'pending';
    }
}

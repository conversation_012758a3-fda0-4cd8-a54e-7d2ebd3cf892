<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Service;

use Comave\SellerPayouts\Helper\Data as SellerPayoutsHelper;
use Comave\SellerPayouts\Helper\Payout\Collections as PayoutCollections;
use Magento\Customer\Model\CustomerFactory;
use Psr\Log\LoggerInterface;
use Stripe\StripeClient;
use Stripe\Payout;
use Stripe\Exception\ApiErrorException;

class StripePayoutService
{
    private const PAYOUT_STATUS_MAPPING = [
        'pending' => 'pending',
        'in_transit' => 'in_transit',
        'paid' => 'paid',
        'failed' => 'failed'
    ];

    /**
     * Cached Stripe client instance
     */
    private ?StripeClient $stripeClient = null;

    /**
     * Cached connected accounts
     */
    private ?array $connectedAccounts = null;

    public function __construct(
        private readonly SellerPayoutsHelper $sellerPayoutsHelper,
        private readonly PayoutCollections $payoutCollections,
        private readonly CustomerFactory $customerFactory,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Get all payouts from Stripe for connected accounts
     */
    public function getAllPayouts(): array
    {
        try {
            $this->logger->info('Starting payout fetch from Stripe');

            $stripeClient = $this->getStripeClient();
            $this->logger->info('Stripe client initialized successfully');

            $connectedAccounts = $this->getConnectedAccounts();
            $this->logger->info('Connected accounts retrieved', ['count' => count($connectedAccounts)]);

            if (empty($connectedAccounts)) {
                $this->logger->warning('No connected accounts found - no payouts will be fetched');
                return [];
            }

            $allPayouts = [];

            foreach ($connectedAccounts as $account) {
                $this->logger->info('Fetching payouts for account', [
                    'seller_id' => $account['seller_id'],
                    'stripe_account_id' => substr($account['stripe_account_id'], 0, 10) . '...'
                ]);

                $payouts = $this->getPayoutsForAccount($stripeClient, $account);
                $this->logger->info('Payouts fetched for account', [
                    'seller_id' => $account['seller_id'],
                    'payout_count' => count($payouts)
                ]);

                $allPayouts = array_merge($allPayouts, $payouts);
            }

            $this->logger->info('Total payouts fetched from Stripe', ['total_count' => count($allPayouts)]);
            return $allPayouts;
        } catch (\Exception $e) {
            $this->logger->error('Error fetching payouts from Stripe', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Get cached Stripe client instance
     *
     * @return StripeClient
     * @throws \Exception
     */
    private function getStripeClient(): StripeClient
    {
        if ($this->stripeClient === null) {
            $secretKey = $this->sellerPayoutsHelper->getDecryptedKey();
            if (empty($secretKey)) {
                throw new \Exception('Stripe secret key not configured');
            }

            $this->stripeClient = new StripeClient($secretKey);
        }

        return $this->stripeClient;
    }

    /**
     * Get cached connected Stripe accounts with optimized query
     *
     * @return array
     */
    private function getConnectedAccounts(): array
    {
        if ($this->connectedAccounts === null) {
            try {
                // Get seller collection and filter by stripe_client_id attribute
                $sellerCollection = $this->payoutCollections->getSellerCollection();

                // Add filter for sellers with stripe_client_id
                $sellerCollection->addAttributeToFilter('stripe_client_id', ['notnull' => true]);
                $sellerCollection->addAttributeToFilter('stripe_client_id', ['neq' => '']);

                // Add customer name attributes
                $sellerCollection->addAttributeToSelect(['firstname', 'lastname', 'stripe_client_id']);

                $this->connectedAccounts = [];
                foreach ($sellerCollection as $seller) {
                    $stripeClientId = $seller->getData('stripe_client_id');
                    if (!empty($stripeClientId)) {
                        $this->connectedAccounts[] = [
                            'seller_id' => $seller->getId(),
                            'seller_name' => trim($seller->getFirstname() . ' ' . $seller->getLastname()) ?: 'Unknown Seller',
                            'stripe_account_id' => $stripeClientId
                        ];
                    }
                }

                $this->logger->info('Connected accounts found', [
                    'count' => count($this->connectedAccounts),
                    'accounts' => array_map(function($account) {
                        return [
                            'seller_id' => $account['seller_id'],
                            'seller_name' => $account['seller_name'],
                            'stripe_account_id' => substr($account['stripe_account_id'], 0, 10) . '...' // Partial for security
                        ];
                    }, $this->connectedAccounts)
                ]);

            } catch (\Exception $e) {
                $this->logger->error('Error getting connected accounts: ' . $e->getMessage());
                $this->connectedAccounts = [];
            }
        }

        return $this->connectedAccounts;
    }

    /**
     * Get payouts for specific Stripe account
     *
     * @param StripeClient $stripeClient
     * @param array $account
     * @return array
     */
    private function getPayoutsForAccount(StripeClient $stripeClient, array $account): array
    {
        try {
            $this->logger->info('Making Stripe API call for payouts', [
                'seller_id' => $account['seller_id'],
                'stripe_account_id' => substr($account['stripe_account_id'], 0, 10) . '...'
            ]);

            $payouts = $stripeClient->payouts->all([
                'limit' => 100,
                'expand' => ['data.destination']
            ], [
                'stripe_account' => $account['stripe_account_id']
            ]);

            $this->logger->info('Stripe API response received', [
                'seller_id' => $account['seller_id'],
                'raw_payout_count' => count($payouts->data)
            ]);

            $formattedPayouts = [];
            foreach ($payouts->data as $payout) {
                $formattedPayouts[] = $this->formatPayoutData($payout, $account);
            }

            return $formattedPayouts;
        } catch (ApiErrorException $e) {
            $this->logger->error('Stripe API error fetching payouts', [
                'error_message' => $e->getMessage(),
                'error_code' => $e->getStripeCode(),
                'seller_id' => $account['seller_id'],
                'stripe_account_id' => substr($account['stripe_account_id'], 0, 10) . '...'
            ]);
            return [];
        } catch (\Exception $e) {
            $this->logger->error('General error fetching payouts', [
                'error_message' => $e->getMessage(),
                'seller_id' => $account['seller_id']
            ]);
            return [];
        }
    }

    /**
     * Format payout data for consistent structure
     *
     * @param Payout $payout
     * @param array $account
     * @return array
     */
    private function formatPayoutData(Payout $payout, array $account): array
    {
        return [
            'stripe_payout_id' => $payout->id,
            'seller_id' => $account['seller_id'],
            'seller_name' => $account['seller_name'],
            'stripe_account_id' => $account['stripe_account_id'],
            'amount' => $payout->amount / 100, // Convert from cents
            'currency' => strtoupper($payout->currency),
            'status' => $this->mapStripeStatus($payout->status),
            'payment_method' => 'stripe',
            'scheduled_date' => $payout->arrival_date ? date('Y-m-d H:i:s', $payout->arrival_date) : null,
            'completion_date' => $payout->status === 'paid' ? date('Y-m-d H:i:s', $payout->created) : null,
            'created_at' => date('Y-m-d H:i:s', $payout->created),
            'last_sync_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Map Stripe payout status to our internal status
     *
     * @param string $stripeStatus
     * @return string
     */
    private function mapStripeStatus(string $stripeStatus): string
    {
        return self::PAYOUT_STATUS_MAPPING[$stripeStatus] ?? 'pending';
    }
}

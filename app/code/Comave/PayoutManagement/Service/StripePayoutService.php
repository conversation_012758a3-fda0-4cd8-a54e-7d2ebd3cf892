<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Service;

use Comave\SellerPayouts\Helper\Data as SellerPayoutsHelper;
use Comave\SellerPayouts\Helper\Payout\Collections as PayoutCollections;
use Magento\Customer\Model\CustomerFactory;
use Psr\Log\LoggerInterface;
use Stripe\StripeClient;
use Stripe\Payout;
use Stripe\Exception\ApiErrorException;

class StripePayoutService
{
    private const PAYOUT_STATUS_MAPPING = [
        'pending' => 'pending',
        'in_transit' => 'in_transit',
        'paid' => 'paid',
        'failed' => 'failed'
    ];

    /**
     * Cached Stripe client instance
     */
    private ?StripeClient $stripeClient = null;

    /**
     * Cached connected accounts
     */
    private ?array $connectedAccounts = null;

    public function __construct(
        private readonly SellerPayoutsHelper $sellerPayoutsHelper,
        private readonly PayoutCollections $payoutCollections,
        private readonly CustomerFactory $customerFactory,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Get all payouts from Stripe for connected accounts
     */
    public function getAllPayouts(): array
    {
        try {
            $stripeClient = $this->getStripeClient();
            $connectedAccounts = $this->getConnectedAccounts();
            $allPayouts = [];

            foreach ($connectedAccounts as $account) {
                $payouts = $this->getPayoutsForAccount($stripeClient, $account);
                $allPayouts = array_merge($allPayouts, $payouts);
            }

            return $allPayouts;
        } catch (\Exception $e) {
            $this->logger->error('Error fetching payouts from Stripe', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Get cached Stripe client instance
     *
     * @return StripeClient
     * @throws \Exception
     */
    private function getStripeClient(): StripeClient
    {
        if ($this->stripeClient === null) {
            $secretKey = $this->sellerPayoutsHelper->getDecryptedKey();
            if (empty($secretKey)) {
                throw new \Exception('Stripe secret key not configured');
            }

            $this->stripeClient = new StripeClient($secretKey);
        }

        return $this->stripeClient;
    }

    /**
     * Get cached connected Stripe accounts with optimized query
     *
     * @return array
     */
    private function getConnectedAccounts(): array
    {
        if ($this->connectedAccounts === null) {
            try {
                // Use optimized query instead of loading full entities
                $sellerCollection = $this->payoutCollections->getSellerCollection();
                $sellerCollection->getSelect()
                    ->reset(\Magento\Framework\DB\Select::COLUMNS)
                    ->columns(['seller_id', 'stripe_client_id'])
                    ->where('stripe_client_id IS NOT NULL AND stripe_client_id != ""');

                $customerCollection = $this->customerFactory->create()->getCollection();
                $customerCollection->getSelect()
                    ->reset(\Magento\Framework\DB\Select::COLUMNS)
                    ->columns(['entity_id', 'CONCAT(firstname, " ", lastname) as seller_name']);

                // Join customer data to get seller names efficiently
                $sellerCollection->getSelect()
                    ->joinLeft(
                        ['customer' => $customerCollection->getMainTable()],
                        'main_table.seller_id = customer.entity_id',
                        ['seller_name' => 'CONCAT(customer.firstname, " ", customer.lastname)']
                    );

                $this->connectedAccounts = [];
                foreach ($sellerCollection as $seller) {
                    $this->connectedAccounts[] = [
                        'seller_id' => $seller->getSellerId(),
                        'seller_name' => $seller->getData('seller_name') ?: 'Unknown Seller',
                        'stripe_account_id' => $seller->getData('stripe_client_id')
                    ];
                }
            } catch (\Exception $e) {
                $this->logger->error('Error getting connected accounts: ' . $e->getMessage());
                $this->connectedAccounts = [];
            }
        }

        return $this->connectedAccounts;
    }

    /**
     * Get payouts for specific Stripe account
     *
     * @param StripeClient $stripeClient
     * @param array $account
     * @return array
     */
    private function getPayoutsForAccount(StripeClient $stripeClient, array $account): array
    {
        try {
            $payouts = $stripeClient->payouts->all([
                'limit' => 100,
                'expand' => ['data.destination']
            ], [
                'stripe_account' => $account['stripe_account_id']
            ]);

            $formattedPayouts = [];
            foreach ($payouts->data as $payout) {
                $formattedPayouts[] = $this->formatPayoutData($payout, $account);
            }

            return $formattedPayouts;
        } catch (ApiErrorException $e) {
            $this->logger->info('Stripe API error fetching payouts', [
                'error_message' => $e->getMessage(),
                'error_code' => $e->getStripeCode(),
                'seller_id' => $account['seller_id']
            ]);
            return [];
        }
    }

    /**
     * Format payout data for consistent structure
     *
     * @param Payout $payout
     * @param array $account
     * @return array
     */
    private function formatPayoutData(Payout $payout, array $account): array
    {
        return [
            'stripe_payout_id' => $payout->id,
            'seller_id' => $account['seller_id'],
            'seller_name' => $account['seller_name'],
            'stripe_account_id' => $account['stripe_account_id'],
            'amount' => $payout->amount / 100, // Convert from cents
            'currency' => strtoupper($payout->currency),
            'status' => $this->mapStripeStatus($payout->status),
            'payment_method' => 'stripe',
            'scheduled_date' => $payout->arrival_date ? date('Y-m-d H:i:s', $payout->arrival_date) : null,
            'completion_date' => $payout->status === 'paid' ? date('Y-m-d H:i:s', $payout->created) : null,
            'created_at' => date('Y-m-d H:i:s', $payout->created),
            'last_sync_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Map Stripe payout status to our internal status
     *
     * @param string $stripeStatus
     * @return string
     */
    private function mapStripeStatus(string $stripeStatus): string
    {
        return self::PAYOUT_STATUS_MAPPING[$stripeStatus] ?? 'pending';
    }
}

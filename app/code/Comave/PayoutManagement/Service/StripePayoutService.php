<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Service;

use Comave\SellerPayouts\Helper\Data as SellerPayoutsHelper;
use Comave\SellerPayouts\Helper\Payout\Collections as PayoutCollections;
use Magento\Customer\Model\CustomerFactory;
use Psr\Log\LoggerInterface;
use Stripe\StripeClient;
use Stripe\Payout;
use Stripe\Exception\ApiErrorException;

class StripePayoutService
{
    private const PAYOUT_STATUS_MAPPING = [
        'pending' => 'pending',
        'in_transit' => 'in_transit',
        'paid' => 'paid',
        'failed' => 'failed'
    ];

    /**
     * Cached Stripe client instance
     */
    private ?StripeClient $stripeClient = null;

    /**
     * Cached connected accounts
     */
    private ?array $connectedAccounts = null;

    public function __construct(
        private readonly SellerPayoutsHelper $sellerPayoutsHelper,
        private readonly PayoutCollections $payoutCollections,
        private readonly CustomerFactory $customerFactory,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Get all payouts from Stripe for connected accounts
     */
    public function getAllPayouts(): array
    {
        try {
            $stripeClient = $this->getStripeClient();
            $connectedAccounts = $this->getConnectedAccounts();

            if (empty($connectedAccounts)) {
                return [];
            }

            $allPayouts = [];

            foreach ($connectedAccounts as $account) {
                try {
                    $payouts = $this->getPayoutsForAccount($stripeClient, $account);
                    $allPayouts = array_merge($allPayouts, $payouts);
                } catch (\Exception $e) {
                    $this->logger->error('Error fetching payouts for account', [
                        'seller_id' => $account['seller_id'],
                        'error' => $e->getMessage()
                    ]);
                    // Continue with other accounts even if one fails
                }
            }

            return $allPayouts;
        } catch (\Exception $e) {
            $this->logger->error('Error fetching payouts from Stripe', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Get cached Stripe client instance
     *
     * @return StripeClient
     * @throws \Exception
     */
    private function getStripeClient(): StripeClient
    {
        if ($this->stripeClient === null) {
            $secretKey = $this->sellerPayoutsHelper->getDecryptedKey();
            if (empty($secretKey)) {
                throw new \Exception('Stripe secret key not configured');
            }

            $this->stripeClient = new StripeClient($secretKey);
        }

        return $this->stripeClient;
    }

    /**
     * Get cached connected Stripe accounts with optimized query
     *
     * @return array
     */
    private function getConnectedAccounts(): array
    {
        if ($this->connectedAccounts === null) {
            $this->connectedAccounts = [];

            try {
                // Get seller collection - this already has stripe_client_id joined as a field
                $sellerCollection = $this->payoutCollections->getSellerCollection();

                // Filter for sellers with stripe_client_id using the proper field reference
                // The field is joined as 'value' from customer_entity_varchar and aliased as 'stripe_client_id'
                $sellerCollection->getSelect()->where('cev.value IS NOT NULL AND cev.value != ""');

                foreach ($sellerCollection as $seller) {
                    $stripeClientId = $seller->getData('stripe_client_id');

                    if (!empty($stripeClientId)) {
                        // Get customer data for seller name
                        $customer = $this->customerFactory->create()->load($seller->getSellerId());
                        $sellerName = trim($customer->getFirstname() . ' ' . $customer->getLastname()) ?: 'Unknown Seller';

                        $this->connectedAccounts[] = [
                            'seller_id' => $seller->getSellerId(),
                            'seller_name' => $sellerName,
                            'stripe_account_id' => $stripeClientId
                        ];
                    }
                }

            } catch (\Exception $e) {
                $this->logger->error('Error getting connected accounts: ' . $e->getMessage());
                $this->connectedAccounts = [];
            }
        }

        return $this->connectedAccounts;
    }

    /**
     * Get payouts for specific Stripe account
     *
     * @param StripeClient $stripeClient
     * @param array $account
     * @return array
     */
    private function getPayoutsForAccount(StripeClient $stripeClient, array $account): array
    {
        try {
            $payouts = $stripeClient->payouts->all([
                'limit' => 100,
                'expand' => ['data.destination']
            ], [
                'stripe_account' => $account['stripe_account_id']
            ]);

            $formattedPayouts = [];
            foreach ($payouts->data as $payout) {
                $formattedPayouts[] = $this->formatPayoutData($payout, $account);
            }

            return $formattedPayouts;
        } catch (ApiErrorException $e) {
            $this->logger->info('Stripe API error fetching payouts', [
                'error_message' => $e->getMessage(),
                'error_code' => $e->getStripeCode(),
                'seller_id' => $account['seller_id']
            ]);
            return [];
        }
    }

    /**
     * Format payout data for consistent structure
     *
     * @param Payout $payout
     * @param array $account
     * @return array
     */
    private function formatPayoutData(Payout $payout, array $account): array
    {
        return [
            'stripe_payout_id' => $payout->id,
            'seller_id' => $account['seller_id'],
            'seller_name' => $account['seller_name'],
            'stripe_account_id' => $account['stripe_account_id'],
            'amount' => $payout->amount / 100, // Convert from cents
            'currency' => strtoupper($payout->currency),
            'status' => $this->mapStripeStatus($payout->status),
            'payment_method' => 'stripe',
            'scheduled_date' => $payout->arrival_date ? date('Y-m-d H:i:s', $payout->arrival_date) : null,
            'completion_date' => $payout->status === 'paid' ? date('Y-m-d H:i:s', $payout->created) : null,
            'created_at' => date('Y-m-d H:i:s', $payout->created),
            'last_sync_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Map Stripe payout status to our internal status
     *
     * @param string $stripeStatus
     * @return string
     */
    private function mapStripeStatus(string $stripeStatus): string
    {
        return self::PAYOUT_STATUS_MAPPING[$stripeStatus] ?? 'pending';
    }
}

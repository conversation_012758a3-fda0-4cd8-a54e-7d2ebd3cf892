<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\ViewModel;

use Comave\PayoutManagement\Service\LastSyncService;
use Magento\Framework\View\Element\Block\ArgumentInterface;

/**
 * Last Sync Info ViewModel
 *
 * Provides last sync information for display in admin templates
 */
class LastSyncInfo implements ArgumentInterface
{
    /**
     * Constructor
     *
     * @param LastSyncService $lastSyncService
     */
    public function __construct(
        private readonly LastSyncService $lastSyncService
    ) {
    }

    /**
     * Get formatted last sync text for display
     *
     * @return string
     */
    public function getLastSyncText(): string
    {
        $lastSync = $this->lastSyncService->getFormattedLastSync();
        return __('Last sync: %1', $lastSync)->render();
    }

    /**
     * Get raw last sync timestamp
     *
     * @return string|null
     */
    public function getLastSyncTimestamp(): ?string
    {
        return $this->lastSyncService->getLastSyncTimestamp();
    }
}

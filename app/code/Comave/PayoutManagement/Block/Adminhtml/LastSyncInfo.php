<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Block\Adminhtml;

use Magento\Framework\View\Element\Template;

/**
 * Last Sync Info Block
 *
 * Simple block that renders last sync information using ViewModel pattern
 */
class LastSyncInfo extends Template
{
    /**
     * Constructor
     *
     * @param Template\Context $context
     * @param array $data
     */
    public function __construct(
        Template\Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }
}

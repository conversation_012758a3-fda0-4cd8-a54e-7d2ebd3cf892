<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Ui\DataProvider;

use Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider;

/**
 * Completed Payouts Data Provider
 *
 * Provides data for completed payouts grid with automatic filtering
 */
class CompletedPayoutsDataProvider extends DataProvider
{
    /**
     * Get data
     *
     * @return array
     */
    public function getData()
    {
        $collection = $this->getCollection();
        
        // Apply filter for completed payouts
        $collection->addFieldToFilter('status', ['in' => ['paid', 'failed']]);
        
        return parent::getData();
    }
}

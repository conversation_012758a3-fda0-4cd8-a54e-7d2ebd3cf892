<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Ui\DataProvider;

use Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider;

/**
 * Upcoming Payouts Data Provider
 *
 * Provides data for upcoming payouts grid with automatic filtering
 */
class UpcomingPayoutsDataProvider extends DataProvider
{
    /**
     * Get data
     *
     * @return array
     */
    public function getData()
    {
        $collection = $this->getCollection();
        
        // Apply filter for upcoming payouts
        $collection->addFieldToFilter('status', ['in' => ['pending', 'in_transit']]);
        
        return parent::getData();
    }
}

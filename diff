diff --git a/app/code/Comave/PayoutManagement/view/adminhtml/ui_component/upcoming_payouts_listing.xml b/app/code/Comave/PayoutManagement/view/adminhtml/ui_component/upcoming_payouts_listing.xml
new file mode 100644
index 000000000..7b589305a
--- /dev/null
+++ b/app/code/Comave/PayoutManagement/view/adminhtml/ui_component/upcoming_payouts_listing.xml
@@ -0,0 +1,131 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
+    <argument name="data" xsi:type="array">
+        <item name="js_config" xsi:type="array">
+            <item name="provider" xsi:type="string">upcoming_payouts_listing.upcoming_payouts_listing_data_source</item>
+        </item>
+    </argument>
+    
+    <settings>
+        <buttons>
+            <button name="refresh" class="Comave\PayoutManagement\Block\Adminhtml\Button\RefreshButton"/>
+        </buttons>
+        <spinner>upcoming_payouts_columns</spinner>
+        <deps>
+            <dep>upcoming_payouts_listing.upcoming_payouts_listing_data_source</dep>
+        </deps>
+    </settings>
+    
+    <dataSource name="upcoming_payouts_listing_data_source" component="Magento_Ui/js/grid/provider">
+        <settings>
+            <storageConfig>
+                <param name="indexField" xsi:type="string">entity_id</param>
+            </storageConfig>
+            <updateUrl path="mui/index/render"/>
+        </settings>
+        <aclResource>Comave_PayoutManagement::upcoming_payouts</aclResource>
+        <dataProvider class="Comave\PayoutManagement\Ui\DataProvider\UpcomingPayoutsDataProvider" name="upcoming_payouts_listing_data_source">
+            <settings>
+                <requestFieldName>id</requestFieldName>
+                <primaryFieldName>entity_id</primaryFieldName>
+            </settings>
+        </dataProvider>
+    </dataSource>
+    
+    <listingToolbar name="listing_top">
+        <settings>
+            <sticky>true</sticky>
+        </settings>
+        <bookmark name="bookmarks"/>
+        <columnsControls name="columns_controls"/>
+        <filterSearch name="fulltext"/>
+        <filters name="listing_filters">
+            <settings>
+                <templates>
+                    <filters>
+                        <select>
+                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
+                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
+                        </select>
+                    </filters>
+                </templates>
+            </settings>
+        </filters>
+        <paging name="listing_paging"/>
+        <exportButton name="export_button">
+            <settings>
+                <selectProvider>upcoming_payouts_listing.upcoming_payouts_listing.upcoming_payouts_columns.ids</selectProvider>
+                <options>
+                    <option name="csv" xsi:type="array">
+                        <item name="value" xsi:type="string">csv</item>
+                        <item name="label" xsi:type="string" translate="true">CSV</item>
+                        <item name="url" xsi:type="string">payout_management/export/upcomingCsv</item>
+                    </option>
+                </options>
+            </settings>
+        </exportButton>
+    </listingToolbar>
+    
+    <columns name="upcoming_payouts_columns">
+        
+        <selectionsColumn name="ids">
+            <settings>
+                <indexField>entity_id</indexField>
+            </settings>
+        </selectionsColumn>
+        
+        <column name="entity_id">
+            <settings>
+                <filter>textRange</filter>
+                <label translate="true">ID</label>
+                <sorting>asc</sorting>
+            </settings>
+        </column>
+        
+        <column name="seller_id">
+            <settings>
+                <filter>textRange</filter>
+                <label translate="true">Seller ID</label>
+            </settings>
+        </column>
+        
+        <column name="seller_name">
+            <settings>
+                <filter>text</filter>
+                <label translate="true">Seller Name</label>
+            </settings>
+        </column>
+        
+        <column name="amount" class="Magento\Ui\Component\Listing\Columns\Column" sortOrder="40">
+            <settings>
+                <filter>textRange</filter>
+                <label translate="true">Payout Amount (EUR)</label>
+            </settings>
+        </column>
+
+        <column name="scheduled_date" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date" sortOrder="50">
+            <settings>
+                <filter>dateRange</filter>
+                <dataType>date</dataType>
+                <label translate="true">Scheduled Payout Date</label>
+            </settings>
+        </column>
+
+        <column name="payment_method" sortOrder="60">
+            <settings>
+                <filter>text</filter>
+                <label translate="true">Payment Method</label>
+            </settings>
+        </column>
+
+        <column name="status" component="Magento_Ui/js/grid/columns/select" sortOrder="70">
+            <settings>
+                <filter>select</filter>
+                <options class="Comave\PayoutManagement\Ui\Component\Listing\Column\Status\UpcomingOptions"/>
+                <dataType>select</dataType>
+                <label translate="true">Payout Status</label>
+            </settings>
+        </column>
+    </columns>
+</listing>
diff --git a/app/code/Comave/PayoutManagement/view/adminhtml/web/js/refresh-payouts.js b/app/code/Comave/PayoutManagement/view/adminhtml/web/js/refresh-payouts.js
new file mode 100644
index 000000000..505be61a3
--- /dev/null
+++ b/app/code/Comave/PayoutManagement/view/adminhtml/web/js/refresh-payouts.js
@@ -0,0 +1,69 @@
+define([
+    'jquery',
+    'Magento_Ui/js/modal/alert',
+    'mage/translate',
+    'loader'
+], function ($, alert, $t) {
+    'use strict';
+
+    return {
+        refresh: function(refreshUrl) {
+            var body = $('body');
+            body.trigger('processStart');
+
+            var formKey = $('input[name="form_key"]').val();
+
+            $.ajax({
+                url: refreshUrl,
+                type: 'POST',
+                dataType: 'json',
+                data: {
+                    form_key: formKey
+                },
+                success: function(response) {
+                    body.trigger('processStop');
+                    
+                    if (response.success) {
+                        alert({
+                            title: $t('Success'),
+                            content: response.message,
+                            actions: {
+                                always: function() {
+                                    window.location.reload();
+                                }
+                            }
+                        });
+                    } else {
+                        alert({
+                            title: $t('Error'),
+                            content: response.message || $t('Failed to refresh payouts')
+                        });
+                    }
+                },
+                error: function(xhr, status, error) {
+                    body.trigger('processStop');
+
+                    var errorMessage = $t('An error occurred while refreshing payouts');
+
+                    if (xhr.responseJSON && xhr.responseJSON.message) {
+                        errorMessage = xhr.responseJSON.message;
+                    } else if (xhr.responseText) {
+                        try {
+                            var response = JSON.parse(xhr.responseText);
+                            if (response.message) {
+                                errorMessage = response.message;
+                            }
+                        } catch (e) {
+                            errorMessage = xhr.statusText || error || errorMessage;
+                        }
+                    }
+
+                    alert({
+                        title: $t('Error'),
+                        content: errorMessage
+                    });
+                }
+            });
+        }
+    };
+});
diff --git a/app/etc/config.php b/app/etc/config.php
index 90f8e539c..d2821b139 100644
--- a/app/etc/config.php
+++ b/app/etc/config.php
@@ -852,6 +852,8 @@ return [
         'Comave_ShopifyAccounts' => 1,
         'Comave_BigBuy' => 1,
         'Comave_ClubGraphQl' => 1,
+        'Comave_SellerPayouts' => 1,
+        'Comave_PayoutManagement' => 1,
         'Webkul_MpSellerCategory' => 1,
         'Comave_SellerReport' => 1,
         'Comave_SellerRequest' => 0,
